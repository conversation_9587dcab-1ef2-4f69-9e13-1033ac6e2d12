'use client';

import { X } from 'lucide-react';
import { useEffect, useState } from 'react';

interface RenameConversationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (newTitle: string) => Promise<void>;
  currentTitle: string;
}

export default function RenameConversationModal({
  isOpen,
  onClose,
  onConfirm,
  currentTitle,
}: RenameConversationModalProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [title, setTitle] = useState(currentTitle);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      setIsAnimating(true);
      setTitle(currentTitle);
      setError('');
    } else {
      setIsAnimating(false);
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 200);
      return () => clearTimeout(timer);
    }
  }, [isOpen, currentTitle]);

  // 处理ESC键关闭
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // 处理背景点击关闭
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && !isSaving) {
      onClose();
    }
  };

  // 处理保存
  const handleSave = async () => {
    const trimmedTitle = title.trim();
    if (!trimmedTitle) {
      setError('名称不能为空');
      return;
    }
    if (trimmedTitle === currentTitle) {
      onClose();
      return;
    }

    setIsSaving(true);
    try {
      await onConfirm(trimmedTitle);
      onClose();
    } catch (error) {
      console.error('重命名失败:', error);
      setError('重命名失败，请重试');
    } finally {
      setIsSaving(false);
    }
  };

  // 处理输入框键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSave();
    }
  };

  if (!isVisible) return null;

  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center transition-all duration-200 ${
        isAnimating ? 'bg-[rgba(0,0,0,0.08)]' : 'bg-black/0'
      }`}
      onClick={handleBackdropClick}
    >
      <div
        className={`bg-white rounded-xl w-[400px] shadow-lg transition-all duration-200 transform ${
          isAnimating
            ? 'scale-100 opacity-100 translate-y-0'
            : 'scale-95 opacity-0 translate-y-4'
        }`}
      >
        {/* 标题区域 */}
        <div className="flex justify-between items-center p-6 pb-6">
          <h3 className="text-lg font-medium text-text-primary">重命名</h3>
          <button
            onClick={onClose}
            disabled={isSaving}
            className={`w-6 h-6 flex items-center justify-center rounded-md transition-all duration-150 cursor-pointer transform hover:scale-110 active:scale-95 ${
              isSaving ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'
            }`}
          >
            <X
              className={`w-4 h-4 transition-colors duration-150 ${
                isSaving
                  ? 'text-gray-400'
                  : 'text-text-muted hover:text-text-primary'
              }`}
            />
          </button>
        </div>

        {/* 内容区域 */}
        <div className="px-6 pb-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-text-primary mb-2">
              名称
            </label>
            <div className="relative">
              <input
                type="text"
                value={title}
                onChange={e => {
                  setTitle(e.target.value);
                  setError('');
                }}
                onKeyDown={handleKeyDown}
                disabled={isSaving}
                className={`w-full px-3 py-2 text-sm border rounded-lg transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-[#ebebeb] bg-[#fafafa]${
                  error
                    ? 'border-[#ebebeb] bg-red-50'
                    : 'border-[#ebebeb] hover:border-[#c2c2c2]'
                } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                placeholder="输入会话名称"
                maxLength={50}
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-text-muted bg-[#f0f0f0] rounded-lg px-2 py-1">
                {title.length}/50
              </div>
            </div>
            {error && <p className="text-sm text-red-500">{error}</p>}
          </div>
        </div>

        {/* 按钮区域 */}
        <div className="flex justify-end gap-3 px-6 pb-6">
          <button
            onClick={onClose}
            disabled={isSaving}
            className={`px-4 py-2 text-sm font-medium text-text-secondary rounded-lg transition-all duration-150 cursor-pointer transform hover:scale-105 active:scale-95 ${
              isSaving
                ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                : 'bg-white border border-gray-300 hover:bg-gray-50'
            }`}
          >
            取消
          </button>
          <button
            onClick={handleSave}
            disabled={
              isSaving || !title.trim() || title.trim() === currentTitle
            }
            className={`px-4 py-2 text-sm font-medium text-white rounded-lg transition-all duration-150 cursor-pointer transform hover:scale-105 active:scale-95 flex items-center gap-2 ${
              isSaving || !title.trim() || title.trim() === currentTitle
                ? 'bg-[#c2c2c2] cursor-not-allowed'
                : 'bg-gray-800 hover:bg-gray-900'
            }`}
          >
            {isSaving && (
              <svg
                className="animate-spin h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            )}
            {isSaving ? '保存中...' : '保存'}
          </button>
        </div>
      </div>
    </div>
  );
}
