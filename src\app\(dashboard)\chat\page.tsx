'use client';

import { getEmployees } from '@/api/employee';
import CardBox from '@/components/common/CardBox';
import SearchInput from '@/components/common/SearchInput';
import { useConversations } from '@/components/history/hooks/useConversations';
import { useAuthContext } from '@/lib/auth/AuthProvider';
import { createConversation } from '@/services/conversation';
import { useRouter } from 'next/navigation';
import React, { useCallback, useEffect, useRef, useState } from 'react';

export default function ChatPage() {
  const { isAuthenticated } = useAuthContext();
  const { addConversation } = useConversations();
  const [search, setSearch] = useState('');
  const [employees, setEmployees] = useState<any[]>([]);
  const [notFound, setNotFound] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1); // 当前页码
  const [hasMore, setHasMore] = useState(true); // 是否还有更多
  const [isSearching, setIsSearching] = useState(false); // 是否处于搜索模式
  const contentRef = useRef<HTMLDivElement>(null); // 内容区ref
  const router = useRouter();
  const PAGE_SIZE = 20;

  // 加载员工数据（分页追加）
  const loadEmployees = useCallback(
    async (reset = false, searchValue = search) => {
      if (loading || (!hasMore && !reset)) return;
      setLoading(true);
      setError(null);
      setNotFound(false);
      try {
        const params: any = {
          'Pager.Page': reset ? 1 : page,
          'Pager.Size': PAGE_SIZE,
          Disabled: false, // 只获取未禁用的员工
          ExcludePersonalCreated: false,
          IsPublic: true,
        };
        if (searchValue) params.Name = searchValue.trim();
        const res = await getEmployees(params);
        if (reset) {
          setEmployees(res.items || []);
          setPage(2);
          setHasMore(res.items && res.items.length === PAGE_SIZE);
          setNotFound(!res.items || res.items.length === 0);
        } else {
          setEmployees(prev => [...prev, ...(res.items || [])]);
          setPage(prev => prev + 1);
          setHasMore(res.items && res.items.length === PAGE_SIZE);
          if ((!res.items || res.items.length === 0) && page === 1)
            setNotFound(true);
        }
      } catch {
        if (reset) setEmployees([]);
        setError('AI员工列表加载失败');
      } finally {
        setLoading(false);
      }
    },
    [loading, hasMore, page, search]
  );

  // 页面加载和搜索框清空时查全部
  useEffect(() => {
    if (!isAuthenticated) return;
    setIsSearching(false);
    setPage(1);
    setHasMore(true);
    loadEmployees(true, '');
  }, [isAuthenticated]);

  // 滚动监听
  useEffect(() => {
    const content = contentRef.current;
    if (!content) return;
    const handleScroll = () => {
      if (loading || !hasMore) return;
      if (
        content.scrollHeight - content.scrollTop - content.clientHeight <
        50
      ) {
        loadEmployees();
      }
    };
    content.addEventListener('scroll', handleScroll);
    return () => content.removeEventListener('scroll', handleScroll);
  }, [loading, hasMore, loadEmployees]);

  // 搜索框回车时远程查找
  const handleSearchKeyPress = async (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      setIsSearching(true);
      setPage(1);
      setHasMore(true);
      await loadEmployees(true, search);
    }
  };

  // 搜索框清空时，恢复所有员工
  const handleClear = () => {
    setSearch('');
    setIsSearching(false);
    setPage(1);
    setHasMore(true);
    loadEmployees(true, '');
  };

  // 新增：点击"开始"按钮时创建会话
  const handleStart = async (employee: any) => {
    try {
      console.log(employee);

      const agentId = employee.agent.agent_id;
      const conversation = await createConversation(agentId);
      if (conversation && conversation.id) {
        // 立即同步新会话到本地状态
        addConversation(conversation);

        // 导航到新会话页面
        router.push(`/history/${conversation.id}`);
      }
    } catch (err) {
      console.error(err);
      alert('创建会话失败');
    }
  };

  return (
    <div className="flex flex-col h-full bg-[#FAFAFA] items-center">
      <div className="flex flex-col items-center justify-start h-full w-[85%]">
        {/* 开场语 */}
        <div className="mt-[160px] flex flex-col items-center justify-center">
          <span className="text-[24px] font-medium text-[#000000E0] mb-[6px]">
            今天准备做什么
          </span>
          <span className="text-[16px] text-[#00000066] font-normal">
            选择一个AI员工开始
          </span>
        </div>

        <div className="flex-1 w-full mb-[40px] mt-3 mb-10 flex flex-col items-center justify-start h-full min-h-0">
          {/* {搜索框} */}
          <div className="w-[75%] mb-8">
            <SearchInput
              value={search}
              onChange={setSearch}
              onClear={handleClear}
              placeholder="搜索..."
              onKeyDown={handleSearchKeyPress}
            />
          </div>
          {/* {内容区} */}
          <div ref={contentRef} className="flex-1 min-h-0 overflow-auto w-full">
            {!isAuthenticated ? (
              <div className="text-center text-gray-400 py-10">请先登录</div>
            ) : error ? (
              <div className="text-center text-red-500 py-10">{error}</div>
            ) : notFound ? (
              <div className="text-center text-gray-400 py-10">
                未找到该AI员工
              </div>
            ) : (
              <>
                <div className="grid grid-cols-3 gap-4 w-full">
                  {employees.map((employee, idx) => (
                    <CardBox
                      key={idx}
                      employee={employee}
                      onStart={() => handleStart(employee)}
                    />
                  ))}
                </div>
                {loading && (
                  <div className="text-center text-gray-400 py-4">
                    加载中...
                  </div>
                )}
                {!hasMore && employees.length > 0 && (
                  <div className="text-center text-gray-400 py-4">
                    没有更多了
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
