'use client';

import MessageViewer from '@/components/history/MessageViewer';
import { useConversations } from '@/components/history/hooks/useConversations';
import { useBreadcrumb } from '@/hooks/usePageConfig';
import { useParams } from 'next/navigation';
import { useEffect } from 'react';

export default function HistoryDetailPage() {
  const params = useParams();
  const conversationId = params.id as string;
  const { getConversationById } = useConversations();
  const { setTitle } = useBreadcrumb();

  // 设置动态面包屑
  useEffect(() => {
    if (!conversationId) return;

    // 尝试获取会话信息
    const conversation = getConversationById(conversationId);

    if (conversation) {
      // 如果找到会话，显示会话标题或默认名称
      const title = conversation.agent_name || `${conversation.title}`;
      setTitle(title, false);
    } else {
      // 如果没找到会话，显示默认标题
      setTitle('历史会话', false);
    }
  }, [conversationId, getConversationById]);

  // 验证 conversationId 参数
  if (!conversationId) {
    return (
      <div className="flex items-center justify-center h-full bg-layer-1">
        <div className="text-center space-y-4">
          <div className="text-error text-lg font-medium">❌ 无效的会话ID</div>
          <div className="text-text-secondary text-sm">
            请检查URL参数是否正确
          </div>
        </div>
      </div>
    );
  }

  return <MessageViewer />;
}
