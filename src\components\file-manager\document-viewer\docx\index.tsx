import { Spin } from 'antd';
import FileError from '../file-error';

import { useFetchDocx } from '../hooks';

// 添加样式
const styles = {
  docxViewerWrapper: {
    overflowY: 'scroll' as const,
    height: '100%',
    width: '100%',
  },
  box: {
    width: '100%',
    height: '100%',
  },
  documentContainer: {
    padding: '30px',
    width: '700px',
    background: 'rgba(255, 255, 255, 0.1)',
    margin: 'auto',
  },
};

const Docx = ({ filePath }: { filePath: string }) => {
  const { succeed, containerRef, error } = useFetchDocx(filePath);

  return (
    <>
      {succeed ? (
        <section className="docxViewerWrapper" style={styles.docxViewerWrapper}>
          <div id="docx" ref={containerRef} className="box" style={styles.box}>
            <Spin />
          </div>
        </section>
      ) : (
        <FileError>{error}</FileError>
      )}
    </>
  );
};

export default Docx;
