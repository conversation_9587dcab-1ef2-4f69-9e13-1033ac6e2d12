import IconKnowledge from '@/assets/knowledge/IconKnowledge.png';
import { getKbDetail } from '@/services/knowledge-service';
import { Button, Divider, Form, Input, Modal, Select } from 'antd';
import Image from 'next/image';
import {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import {
  useCreateKnowledge,
  useUpdateKnowledge,
} from '../_hooks/knowledge-hooks';

const Option = Select.Option;
const FormItem = Form.Item;
const TextArea = Input.TextArea;

// 新增/修改知识库
const KnowledgeModal = forwardRef((props, ref) => {
  const [visible, setVisible] = useState(false);

  const { loading: createLoading, createKnowledge } = useCreateKnowledge();

  const { saveKnowledgeConfiguration, loading: updateLoading } =
    useUpdateKnowledge(true);

  const [form] = Form.useForm();

  const permission = Form.useWatch('permission', form);

  const id = Form.useWatch('id', form);

  const [tags, setTags] = useState<string[]>([]);

  const confirmLoading = useMemo(
    () => (id ? updateLoading : createLoading),
    [id, createLoading]
  );

  // const [tags, dispatchTag] = useReducer((state, action) => {
  //   switch (action.type) {
  //     case 'add':
  //       return [...state, `标签${state.length + 1}`];
  //     case 'remove':
  //       const _state = state.slice();
  //       _state.splice(action.index, 1);
  //       return _state;
  //   }
  // }, []);

  const onOpen = async (row?: any) => {
    form.resetFields();
    if (row) {
      const { id } = row;
      const {
        data: { data: knowledgeDetails },
      } = await getKbDetail(id);
      if (!knowledgeDetails) return;
      form.setFieldsValue(knowledgeDetails);
      setTags(knowledgeDetails.tags || []);
    }
    setVisible(true);
  };

  useImperativeHandle(ref, () => ({
    open: onOpen,
  }));

  const title = useMemo<string>(() => {
    return id ? '修改库' : '创建库';
  }, [id]);

  const hideModal = useCallback(() => {
    setVisible(false);
  }, []);

  const handleOk = async (ret: any) => {
    const { id, ...values } = ret;
    if (id) {
      await saveKnowledgeConfiguration({
        ...values,
        kb_id: id,
        tags: tags,
      });
    } else {
      await createKnowledge(values);
    }
    hideModal();
    setTags([]);
  };

  // 新增标签输入项
  const showTagInput = () => {
    if (tags?.length >= 3) {
      // 这里可以添加提示信息
      return;
    }

    setTags([...tags, '']);
  };

  // 处理标签变化
  const handleTagChange = (newTags: string[]) => {
    setTags(newTags);
  };

  return (
    <Modal
      open={visible}
      title={title}
      cancelText="取消"
      okText="创建"
      className="min-w-[640px] [&_.arco-modal-content]:p-[24px] top-[5%]"
      onCancel={() => {
        setVisible(false);
        setTags([]);
      }}
      confirmLoading={confirmLoading}
      okButtonProps={{
        autoFocus: true,
        htmlType: 'submit',
        color: 'default',
        variant: 'solid',
      }}
      destroyOnHidden
      forceRender
      modalRender={dom => (
        <Form
          layout="vertical"
          form={form}
          clearOnDestroy
          onFinish={values => handleOk(values)}
        >
          {dom}
        </Form>
      )}
    >
      <FormItem name="id" hidden>
        <Input hidden />
      </FormItem>
      <FormItem name="parser_id" hidden>
        <Input hidden />
      </FormItem>
      {/* 名称 */}
      <div className="grid grid-cols-[auto_auto_1fr] colum mb-[24px]">
        <Image
          className="shrink-0"
          src={IconKnowledge}
          width={72}
          height={72}
          alt=""
        />
        <Divider className="h-full mx-[24px]" type="vertical" />
        <FormItem
          className="mb-0"
          label="名称"
          name="name"
          required
          rules={[
            {
              required: true,
              message: '请输入名称',
            },
          ]}
        >
          <Input placeholder="请输入" />
        </FormItem>
      </div>
      {/* 描述 */}
      <FormItem label="描述" name="description">
        <TextArea
          className="min-h-[64px] resize-none"
          placeholder="请输入"
          maxLength={100}
          count={{
            show: true,
          }}
        />
      </FormItem>
      {/* 标签 */}
      <FormItem>
        <div className="mt-[20px]">
          <div className="flex items-center justify-between mb-[8px]">
            <div>
              <span>标签</span>
              <span className="text-[#adadad] text-[12px]">
                （标签至多添加3个）
              </span>
            </div>
            <Button className="add-tag-btn" onClick={showTagInput}>
              添加
            </Button>
          </div>
          {tags?.length > 0 && (
            <div className="selected-item-list">
              {(tags || []).map((tag, index) => (
                <div key={`tag-${index}`} className="selected-item-row">
                  <Input
                    autoFocus={tag === ''}
                    value={tag}
                    onChange={e => {
                      const value = e.target.value;
                      if (value && value.length > 20) {
                        return;
                      }
                      const newTags = [...tags];
                      newTags[index] = value;
                      handleTagChange(newTags);
                    }}
                    onKeyDown={e => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        e.stopPropagation();
                      }
                    }}
                    placeholder={`标签 ${index + 1}`}
                    suffix={
                      <div
                        className="delete-icon"
                        onClick={() => {
                          const newTags = [...tags];
                          newTags.splice(index, 1);
                          handleTagChange(newTags);
                        }}
                      />
                    }
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      </FormItem>
      {/* 语言 */}
      <FormItem label="语言" name="language" initialValue="zh-CN">
        <Select>
          <Option value={'zh-CN'}>中文</Option>
          <Option value={'en'}>英文</Option>
        </Select>
      </FormItem>
      {/* 权限 */}
      <FormItem label="权限">
        <FormItem name="permission" initialValue={'public'}>
          <Select>
            <Option value={'me'}>个人</Option>
            <Option value={'public'}>共享</Option>
          </Select>
        </FormItem>
      </FormItem>
    </Modal>
  );
});

export default KnowledgeModal;
