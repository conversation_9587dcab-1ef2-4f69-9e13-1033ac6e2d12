'use client';

import MessageList from '@/components/chat/MessageList';
import { useChatMessages } from '@/components/history/hooks/useChatMessages';
import { useEffect } from 'react';
import ChatInput from '../chat/ChatInput';
import { useHistoryContext } from './HistoryProvider';
import { useRouteSync } from './hooks/useRouteSync';

export default function MessageViewer() {
  const { userId } = useHistoryContext();
  const { currentConversationId } = useRouteSync(userId);
  const { error, loadMessages, clearError, hasError } = useChatMessages(
    currentConversationId || undefined
  );

  // 当会话ID变化时加载消息
  useEffect(() => {
    if (currentConversationId) {
      console.log('📂 [MessageViewer] 加载会话消息:', {
        conversationId: currentConversationId,
      });
      loadMessages(currentConversationId);
    }
  }, [currentConversationId, loadMessages]);

  // 错误状态处理
  if (hasError) {
    return (
      <div className="flex flex-col h-full items-center justify-center space-y-4">
        <div className="text-center">
          <svg
            className="w-12 h-12 mx-auto text-error mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <div className="text-error text-sm font-medium">加载消息出现错误</div>
          <div className="text-text-muted text-xs mt-1">{error}</div>
        </div>
        <button
          onClick={() => {
            clearError();
            if (currentConversationId) {
              loadMessages(currentConversationId);
            }
          }}
          className="px-4 py-2 bg-primary text-bg-base rounded-lg hover:bg-blue-600 transition-colors text-sm"
        >
          重试
        </button>
      </div>
    );
  }

  // 无会话ID时的提示
  if (!currentConversationId) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center space-y-4">
          <svg
            className="w-16 h-16 mx-auto text-text-muted"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
            />
          </svg>
          <div>
            <div className="text-text-muted text-sm">
              请选择一个会话开始查看
            </div>
            <div className="text-text-muted text-xs mt-1">
              从左侧会话列表中选择一个会话
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-layer-1">
      {/* 消息区域 */}
      <div className="flex-1 min-h-0 overflow-hidden">
        <MessageList />
      </div>

      {/* 底部输入区域 - 固定在底部 */}
      <div className="flex-shrink-0 p-6">
        <ChatInput />
      </div>
    </div>
  );
}
