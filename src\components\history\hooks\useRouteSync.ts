'use client';

import { useParams, usePathname, useRouter } from 'next/navigation';
import { useCallback, useEffect } from 'react';
import { useConversations } from './useConversations';

export interface UseRouteSyncReturn {
  // 当前路由状态
  currentConversationId: string | null;

  // 路由操作
  navigateToConversation: (conversationId: string) => void;
  navigateToHistory: () => void;

  // 工具方法
  isValidConversationRoute: boolean;
}

export const useRouteSync = (userId?: string): UseRouteSyncReturn => {
  const router = useRouter();
  const pathname = usePathname();
  const params = useParams();

  const {
    conversations,
    setSelectedConversation,
    loadConversations,
    getConversationById,
  } = useConversations();

  // 从URL获取当前会话ID
  const currentConversationId = (params.id as string) || null;

  // 检查当前路由是否为有效的会话路由
  const isValidConversationRoute = Boolean(
    currentConversationId &&
      pathname.includes('/history/') &&
      getConversationById(currentConversationId)
  );

  // 路由操作方法
  const navigateToConversation = useCallback(
    (conversationId: string) => {
      router.push(`/history/${conversationId}`);
    },
    [router]
  );

  const navigateToHistory = useCallback(() => {
    router.push('/history');
  }, [router]);

  // 同步URL变化到store状态
  useEffect(() => {
    if (currentConversationId !== null && conversations.length > 0) {
      setSelectedConversation(currentConversationId);
    }
  }, [currentConversationId, conversations, setSelectedConversation]);

  // 确保有用户ID时加载会话列表（仅在首次渲染时）
  useEffect(() => {
    if (userId && conversations.length === 0) {
      console.log('🔄 [useRouteSync] 初始化加载会话列表:', { userId });
      loadConversations(userId).catch(error => {
        console.error('❌ [useRouteSync] 加载会话列表失败:', error);
      });
    }
  }, [userId, conversations.length, loadConversations]);

  // 自动导航逻辑：当在根路径且有可用会话时
  useEffect(() => {
    if (
      pathname === '/history' &&
      !currentConversationId &&
      conversations.length > 0 &&
      userId
    ) {
      // 选择最新的会话
      const latestConversation = conversations[0];
      if (latestConversation) {
        console.log('🎯 [useRouteSync] 自动导航到最新会话:', {
          conversationId: latestConversation.id,
        });
        navigateToConversation(latestConversation.id);
      }
    }
  }, [
    pathname,
    currentConversationId,
    conversations,
    userId,
    navigateToConversation,
  ]);

  // 验证会话ID的有效性
  useEffect(() => {
    if (
      currentConversationId &&
      conversations.length > 0 &&
      !getConversationById(currentConversationId)
    ) {
      console.warn('⚠️ [useRouteSync] 当前会话ID无效，重定向到历史页面:', {
        conversationId: currentConversationId,
      });
      navigateToHistory();
    }
  }, [
    currentConversationId,
    conversations,
    getConversationById,
    navigateToHistory,
  ]);

  return {
    // 当前路由状态
    currentConversationId,

    // 路由操作
    navigateToConversation,
    navigateToHistory,

    // 工具方法
    isValidConversationRoute,
  };
};
