import type { NextConfig } from 'next';

let authConfig;
const nextConfig: NextConfig = async () => {
  const apiUrl = process.env.NEXT_PUBLIC_API_URL;
  if (!apiUrl) {
    throw new Error('NEXT_PUBLIC_API_URL is not set');
  }

  authConfig = process.env.NEXT_PUBLIC_AUTH_CONFIG;
  if (!authConfig) {
    const response = await fetch(`${apiUrl}/authconfig`);
    if (response.ok) {
      authConfig = await response.text();
    } else {
      throw new Error('无法获取认证配置');
    }
  }
  console.log('authConfig', authConfig);

  return {
    // Docker 容器化输出模式
    output: 'standalone',

    // 关闭TypeScript类型检查以解决构建问题
    typescript: {
      ignoreBuildErrors: true,
    },
    eslint: {
      ignoreDuringBuilds: true,
    },
    // 启用实验性功能以提升性能
    experimental: {
      optimizeCss: true, // 暂时关闭，需要critters依赖
      optimizePackageImports: [
        'ahooks',
        'antd',
        'lucide-react',
        'react-pdf-highlighter',
      ],
    },
    turbopack: {
      rules: {
        '*.svg': {
          loaders: ['@svgr/webpack'],
          as: '*.js',
        },
      },
      resolveAlias: {
        canvas: './turbopack-canvas-stub.js',
      },
    },
    webpack(config: any) {
      config.module.rules.push({
        test: /\.svg$/,
        use: ['@svgr/webpack'],
      });

      // 解决 pdfjs-dist 的 Node.js 模块依赖问题
      config.resolve.alias = {
        ...config.resolve.alias,
        canvas: false,
      };

      return config;
    },
    env: {
      // 将获取到的配置序列化为环境变量
      NEXT_PUBLIC_AUTH_CONFIG: authConfig,
    },

    async rewrites() {
      return [
        {
          source: '/api/:path*',
          destination: `${process.env.NEXT_PUBLIC_API_URL}/:path*`,
        },
        {
          source: '/ws/:path*',
          destination: `${process.env.NEXT_PUBLIC_API_URL}/:path*`,
        },
        {
          source: '/v1/:path*',
          destination: `${process.env.NEXT_PUBLIC_ZIKO_API_URL}/v1/:path*`,
        },
      ];
    },
  };
};

export default nextConfig;
