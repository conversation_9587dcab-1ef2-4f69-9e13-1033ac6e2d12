import { Authorization, Token, UserInfo } from '@/constants/authorization';
import { getUserStore } from '@/lib/helpers/store';
import { getSearchValue } from './common-util';

const KeycloakToken = 'KeycloakToken';
const KeySet = [Authorization, Token, UserInfo, KeycloakToken];

const storage = {
  getAuthorization: () => {
    return localStorage.getItem(Authorization);
  },
  getToken: () => {
    return localStorage.getItem(Token);
  },
  getUserInfo: () => {
    return localStorage.getItem(UserInfo);
  },
  getUserInfoObject: () => {
    return JSON.parse(localStorage.getItem('userInfo') || '');
  },
  setAuthorization: (value: string) => {
    localStorage.setItem(Authorization, value);
  },
  setToken: (value: string) => {
    localStorage.setItem(Token, value);
  },
  setUserInfo: (value: string | Record<string, unknown>) => {
    const valueStr = typeof value !== 'string' ? JSON.stringify(value) : value;
    localStorage.setItem(UserInfo, valueStr);
  },
  setItems: (pairs: Record<string, string>) => {
    Object.entries(pairs).forEach(([key, value]) => {
      localStorage.setItem(key, value);
    });
  },
  removeAuthorization: () => {
    localStorage.removeItem(Authorization);
  },
  removeAll: () => {
    KeySet.forEach(x => {
      localStorage.removeItem(x);
    });
  },
  setLanguage: (lng: string) => {
    localStorage.setItem('lng', lng);
  },
  getLanguage: (): string => {
    return localStorage.getItem('lng') as string;
  },
  setKeycloakToken: (token: string) => {
    localStorage.setItem(KeycloakToken, token);
  },
  getKeycloakToken: () => {
    return localStorage.getItem(KeycloakToken);
  },
  removeKeycloakToken: () => {
    localStorage.removeItem(KeycloakToken);
  },
};

// 清理token中的双引号和单引号
const cleanToken = (token: string): string => {
  return token.replace(/"/g, '').replace(/'/g, '');
};

export const getAuthorization = () => {
  // 1. 首先尝试获取主项目的Keycloak token（存储在authToken中）
  try {
    const authToken = localStorage.getItem('authToken');
    if (authToken) {
      console.log('🔑 使用主项目Keycloak token');
      return `Bearer ${cleanToken(authToken)}`;
    }
  } catch (error) {
    console.warn('获取主项目Keycloak token失败:', error);
  }

  // 2. 尝试获取主项目的其他token
  try {
    const mainProjectUser = getUserStore();
    if (mainProjectUser && mainProjectUser.token) {
      console.log('🔑 使用主项目store token');
      return `Bearer ${cleanToken(mainProjectUser.token)}`;
    }
  } catch (error) {
    console.warn('获取主项目store token失败:', error);
  }

  // 3. 检查开发环境
  const isDevelopment =
    (typeof window !== 'undefined' &&
      window.location.hostname === 'localhost') ||
    (typeof window !== 'undefined' &&
      window.location.hostname.includes('localhost')) ||
    (typeof window !== 'undefined' && window.location.hostname === '127.0.0.1');

  if (isDevelopment) {
    // 开发环境：尝试使用localStorage中存储的开发token
    const devToken = localStorage.getItem('devToken');
    if (devToken) {
      console.log('🔑 开发环境：使用存储的开发token');
      return `Bearer ${cleanToken(devToken)}`;
    }

    // 如果没有存储的开发token，使用固定token（作为最后的备选）
    const fixedToken =
      '***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
    console.log('🔑 开发环境：使用固定的备用token');
    return `Bearer ${fixedToken}`;
  }

  // 4. 检查其他Keycloak token
  const keycloakToken = storage.getKeycloakToken();
  if (keycloakToken) {
    console.log('🔑 使用其他Keycloak token');
    return `Bearer ${cleanToken(keycloakToken)}`;
  }

  // 5. 检查URL参数中的auth
  const auth = getSearchValue('auth');
  if (auth) {
    console.log('🔑 使用URL参数中的auth');
    return `Bearer ${cleanToken(auth)}`;
  }

  // 6. 检查存储的Authorization
  const storedAuth = storage.getAuthorization();
  if (storedAuth) {
    console.log('🔑 使用存储的Authorization');
    // 如果存储的Authorization已经包含Bearer前缀，直接返回
    if (storedAuth.startsWith('Bearer ')) {
      return storedAuth;
    }
    // 否则清理token中的双引号并添加Bearer前缀
    return `Bearer ${cleanToken(storedAuth)}`;
  }

  console.log('🔑 未找到有效token');
  return '';
};

// 设置Keycloak认证信息
export const setKeycloakAuth = (token: string, userInfo?: any) => {
  storage.setKeycloakToken(token);
  if (userInfo) {
    storage.setUserInfo(userInfo);
  }
};

// 新增：设置开发环境token的便捷方法
export const setDevToken = (token: string) => {
  localStorage.setItem('devToken', token);
  console.log('🔑 已设置开发环境token');
};

// 新增：获取当前使用的token信息
export const getTokenInfo = () => {
  try {
    const mainProjectUser = getUserStore();
    const authToken = localStorage.getItem('authToken');
    return {
      hasMainProjectKeycloakToken: !!authToken,
      hasMainProjectStoreToken: !!(mainProjectUser && mainProjectUser.token),
      hasKeycloakToken: !!storage.getKeycloakToken(),
      hasDevToken: !!localStorage.getItem('devToken'),
      hasUrlAuth: !!getSearchValue('auth'),
      hasStoredAuth: !!storage.getAuthorization(),
    };
  } catch (error) {
    const authToken = localStorage.getItem('authToken');
    return {
      hasMainProjectKeycloakToken: !!authToken,
      hasMainProjectStoreToken: false,
      hasKeycloakToken: !!storage.getKeycloakToken(),
      hasDevToken: !!localStorage.getItem('devToken'),
      hasUrlAuth: !!getSearchValue('auth'),
      hasStoredAuth: !!storage.getAuthorization(),
      // error: error.message,
    };
  }
};

export default storage;

// Will not jump to the login page
export function redirectToLogin() {
  window.location.href = location.origin + `/login`;
}
