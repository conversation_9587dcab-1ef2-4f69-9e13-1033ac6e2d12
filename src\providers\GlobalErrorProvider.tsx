'use client';

/**
 * 全局错误处理 Provider
 * 统一管理所有全局错误，使用友好的方式处理：
 * - 401认证错误：显示登录弹窗
 * - 其他错误：显示toast通知
 */

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useEffect,
} from 'react';
import { useLocalStorageState } from 'ahooks';
import {
  setGlobalErrorHandler,
  getRequestQueueStatus,
} from '@/lib/http/errorInterceptor';
import { setGlobalErrorTrigger } from '@/lib/http/errorUtils';
import toast, { Toaster } from 'react-hot-toast';

// ============== 类型定义 ==============

/** 错误类型枚举 */
export enum ErrorType {
  NETWORK = 'NETWORK', // 网络错误
  AUTH = 'AUTH', // 认证错误
  PERMISSION = 'PERMISSION', // 权限错误
  SERVER = 'SERVER', // 服务器错误
  VALIDATION = 'VALIDATION', // 验证错误
  TIMEOUT = 'TIMEOUT', // 超时错误
  UNKNOWN = 'UNKNOWN', // 未知错误
}

/** 错误严重程度枚举 */
export enum ErrorSeverity {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  CRITICAL = 4,
}

/** 全局错误对象 */
export interface GlobalError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  title: string;
  message: string;
  details?: any;
  source?: string;
  timestamp: number;
  retryable?: boolean;
  autoRetryCount?: number;
  maxRetries?: number;
}

/** 错误处理配置 */
export interface ErrorHandlerConfig {
  autoRetry?: boolean;
  maxAutoRetries?: number;
  retryDelay?: number;
  showNotification?: boolean;
  persistent?: boolean;
  actions?: Array<{
    label: string;
    action: () => void;
    primary?: boolean;
  }>;
}

/** Context 状态 */
interface GlobalErrorContextState {
  // 错误状态
  errors: GlobalError[];
  pendingRequestsCount: number;

  // 错误处理方法
  addError: (
    error: Partial<GlobalError> & { message: string; type: ErrorType }
  ) => void;
  removeError: (errorId: string) => void;
  clearAllErrors: () => void;
  retryError: (errorId: string) => void;

  // 观察者模式方法
  registerErrorObserver: (
    id: string,
    observer: (error: GlobalError) => void
  ) => void;
  unregisterErrorObserver: (id: string) => void;

  // 配置方法
  setErrorConfig: (config: Partial<ErrorHandlerConfig>) => void;
}

// ============== Context ==============

const GlobalErrorContext = createContext<GlobalErrorContextState | undefined>(
  undefined
);

// ============== Provider Props ==============

interface GlobalErrorProviderProps {
  children: React.ReactNode;
  config?: Partial<ErrorHandlerConfig>;
}

// ============== Provider ==============

export function GlobalErrorProvider({
  children,
  config = {},
}: GlobalErrorProviderProps) {
  // 状态管理 - 只保留错误管理相关
  const [errors, setErrors] = useState<GlobalError[]>([]);
  const [pendingRequestsCount, setPendingRequestsCount] = useState(0);

  // 观察者模式：错误处理器注册
  const [errorObservers, setErrorObservers] = useState<
    Map<string, (error: GlobalError) => void>
  >(new Map());

  // 配置管理（持久化）
  const [errorConfig, setErrorConfig] =
    useLocalStorageState<ErrorHandlerConfig>('global-error-config', {
      defaultValue: {
        autoRetry: true,
        maxAutoRetries: 3,
        retryDelay: 2000,
        showNotification: true,
        persistent: false,
        ...config,
      },
    });

  // ============== Toast通知方法 ==============

  /** 显示错误toast通知 */
  const showErrorToast = useCallback((error: GlobalError) => {
    const getErrorIcon = (type: ErrorType) => {
      switch (type) {
        case ErrorType.NETWORK:
          return '🌐';
        case ErrorType.SERVER:
          return '⚠️';
        case ErrorType.PERMISSION:
          return '🔒';
        case ErrorType.VALIDATION:
          return '📝';
        case ErrorType.TIMEOUT:
          return '⏰';
        default:
          return '❗';
      }
    };

    const getToastType = (severity: ErrorSeverity) => {
      switch (severity) {
        case ErrorSeverity.CRITICAL:
        case ErrorSeverity.HIGH:
          return 'error';
        case ErrorSeverity.MEDIUM:
          return 'warning';
        case ErrorSeverity.LOW:
        default:
          return 'info';
      }
    };

    const icon = getErrorIcon(error.type);
    const message = `${icon} ${error.message}`;

    const toastType = getToastType(error.severity);

    if (toastType === 'error') {
      toast.error(message, { duration: 4000 });
    } else if (toastType === 'warning') {
      toast(message, {
        icon: '⚠️',
        duration: 3000,
        style: {
          background: '#fef3c7',
          border: '1px solid #f59e0b',
          color: '#92400e',
        },
      });
    } else {
      toast(message, { duration: 2000 });
    }
  }, []);

  // ============== 错误处理核心方法 ==============

  /** 添加错误 */
  const addError = useCallback(
    (
      errorInput: Partial<GlobalError> & { message: string; type: ErrorType }
    ) => {
      const error: GlobalError = {
        id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        severity: ErrorSeverity.MEDIUM,
        title: getDefaultErrorTitle(errorInput.type),
        timestamp: Date.now(),
        retryable: getDefaultRetryable(errorInput.type),
        autoRetryCount: 0,
        maxRetries: errorConfig?.maxAutoRetries || 3,
        ...errorInput,
      };

      console.error('🚨 全局错误:', error);

      // 添加到错误历史记录
      setErrors(prev => [...prev, error]);

      // 通知所有观察者
      errorObservers.forEach((observer, id) => {
        try {
          observer(error);
        } catch (err) {
          console.warn(`观察者 ${id} 处理错误时出错:`, err);
        }
      });

      // 根据错误类型处理 - 只处理非认证错误
      if (error.type !== ErrorType.AUTH) {
        // 非认证错误 - 使用toast通知
        console.warn(`⚠️ ${error.type}错误:`, error.message);
        showErrorToast(error);
      }
      // 认证错误交给AuthProvider处理，这里不做任何处理

      // // 自动重试逻辑
      // if (
      //   error.retryable &&
      //   errorConfig?.autoRetry &&
      //   (error.autoRetryCount || 0) < (error.maxRetries || 3)
      // ) {
      //   scheduleAutoRetry(error);
      // }
    },
    [errorConfig, showErrorToast, errorObservers]
  );

  /** 移除错误 */
  const removeError = useCallback((errorId: string) => {
    setErrors(prev => prev.filter(e => e.id !== errorId));
  }, []);

  /** 清空所有错误 */
  const clearAllErrors = useCallback(() => {
    setErrors([]);
  }, []);

  /** 重试错误 */
  const retryError = useCallback(
    (errorId: string) => {
      const error = errors.find(e => e.id === errorId);
      if (!error || !error.retryable) return;

      // 移除当前错误
      removeError(errorId);

      console.log('🔄 重试错误:', error);
      // 实际重试逻辑需要根据错误类型来实现
    },
    [errors, removeError]
  );

  /** 注册错误观察者 */
  const registerErrorObserver = useCallback(
    (id: string, observer: (error: GlobalError) => void) => {
      setErrorObservers(prev => {
        const newObservers = new Map(prev);
        newObservers.set(id, observer);
        return newObservers;
      });
      console.log(`✅ 注册错误观察者: ${id}`);
    },
    []
  );

  /** 取消注册错误观察者 */
  const unregisterErrorObserver = useCallback((id: string) => {
    setErrorObservers(prev => {
      const newObservers = new Map(prev);
      newObservers.delete(id);
      return newObservers;
    });
    console.log(`❌ 取消注册错误观察者: ${id}`);
  }, []);

  /** 安排自动重试 */
  const scheduleAutoRetry = useCallback(
    (error: GlobalError) => {
      const delay = errorConfig?.retryDelay || 2000;

      setTimeout(() => {
        setErrors(prev => {
          const index = prev.findIndex(e => e.id === error.id);
          if (index === -1) return prev;

          const updatedError = {
            ...prev[index],
            autoRetryCount: (prev[index].autoRetryCount || 0) + 1,
          };

          console.log(
            `🔄 自动重试第 ${updatedError.autoRetryCount} 次:`,
            updatedError.message
          );

          // 触发重试逻辑
          retryError(error.id);

          return prev;
        });
      }, delay);
    },
    [errorConfig, retryError]
  );

  // ============== 移除认证状态监听 - 认证由AuthProvider自行处理 =============="

  // ============== 初始化全局错误处理器 ==============

  useEffect(() => {
    // 设置全局错误处理器，让HTTP拦截器能够调用
    setGlobalErrorHandler(error => {
      addError(error);
    });

    // 设置API管理器的错误处理器
    (async () => {
      try {
        const { setGlobalErrorHandler: setApiManagerErrorHandler } =
          await import('@/services/apiManager');
        setApiManagerErrorHandler(error => {
          addError(error);
        });
      } catch (err) {
        console.warn('未能加载apiManager错误处理器:', err);
      }
    })();

    // 设置全局错误触发器，让工具函数能够调用
    setGlobalErrorTrigger(error => {
      addError(error);
    });
  }, [addError]);

  // ============== 请求队列状态监听 ==============

  useEffect(() => {
    // 定期更新请求队列状态
    const updateQueueStatus = () => {
      const status = getRequestQueueStatus();
      setPendingRequestsCount(status.pendingCount);
    };

    updateQueueStatus();
    const interval = setInterval(updateQueueStatus, 1000);

    return () => clearInterval(interval);
  }, []);

  // ============== 移除所有认证状态监听 - 认证由AuthProvider自行处理 =============="

  // ============== Context 值 ==============

  const contextValue: GlobalErrorContextState = {
    // 错误状态
    errors,
    pendingRequestsCount,

    // 错误处理方法
    addError,
    removeError,
    clearAllErrors,
    retryError,

    // 观察者模式方法
    registerErrorObserver,
    unregisterErrorObserver,

    // 配置方法
    setErrorConfig: newConfig =>
      setErrorConfig(prev => ({ ...prev, ...newConfig })),
  };

  return (
    <GlobalErrorContext.Provider value={contextValue}>
      {children}

      {/* Toast通知容器 - 只处理非认证错误 */}
      <Toaster
        position="top-center"
        toastOptions={{
          duration: 3000,
          style: {
            background: 'var(--bg-base)',
            color: 'var(--text-primary)',
            border: '1px solid var(--border-light)',
            borderRadius: '12px',
            boxShadow: '0 10px 40px rgba(0, 0, 0, 0.1)',
          },
          success: {
            style: {
              background: '#f0f9ff',
              border: '1px solid #0ea5e9',
              color: '#0c4a6e',
            },
          },
          error: {
            style: {
              background: '#fef2f2',
              border: '1px solid #ef4444',
              color: '#991b1b',
            },
          },
        }}
      />
    </GlobalErrorContext.Provider>
  );
}

// ============== Hook ==============

export function useGlobalError(): GlobalErrorContextState {
  const context = useContext(GlobalErrorContext);
  if (context === undefined) {
    throw new Error('useGlobalError must be used within a GlobalErrorProvider');
  }
  return context;
}

// ============== 辅助函数 ==============

function getDefaultErrorTitle(type: ErrorType): string {
  switch (type) {
    case ErrorType.AUTH:
      return '认证失败';
    case ErrorType.PERMISSION:
      return '权限不足';
    case ErrorType.NETWORK:
      return '网络错误';
    case ErrorType.SERVER:
      return '服务器错误';
    case ErrorType.VALIDATION:
      return '数据验证失败';
    case ErrorType.TIMEOUT:
      return '请求超时';
    default:
      return '操作失败';
  }
}

function getDefaultRetryable(type: ErrorType): boolean {
  switch (type) {
    case ErrorType.NETWORK:
    case ErrorType.TIMEOUT:
    case ErrorType.SERVER:
      return true;
    case ErrorType.AUTH:
    case ErrorType.PERMISSION:
    case ErrorType.VALIDATION:
    default:
      return false;
  }
}
