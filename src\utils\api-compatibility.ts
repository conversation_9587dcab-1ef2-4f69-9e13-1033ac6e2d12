/**
 * API 兼容性工具
 * 用于处理不同API响应格式之间的兼容性问题
 */

import { ConversationItem } from '@/types/conversation';

/**
 * 标准化会话列表响应
 * 将各种可能的响应格式转换为统一的格式
 */
export function conversationsResponse(
  page: number,
  size: number,
  response: {
    items: ConversationItem[];
    count: number;
  }
): {
  data: ConversationItem[];
  pagination?: {
    total: number;
    currentPage: number;
    totalPages: number;
    hasMore: boolean;
  };
} {
  return {
    data: response.items,
    pagination: {
      total: response.count,
      currentPage: page,
      totalPages: Math.ceil(response.count / size),
      hasMore: page < Math.ceil(response.count / size),
    },
  };
}

/**
 * 检查响应是否支持分页
 */
export function isApiPaginationSupported(response: any): boolean {
  return (
    response &&
    typeof response === 'object' &&
    response.pager &&
    typeof response.pager.total === 'number'
  );
}

/**
 * 构建兼容的分页参数
 * 当API不支持分页时，使用默认参数
 */
export function buildCompatiblePagerParams(
  page: number,
  size: number,
  supportsPagination: boolean = true
): any {
  if (!supportsPagination) {
    // 如果不支持分页，只传递基础参数
    return {
      page: 1,
      size: 100, // 尝试获取更多数据
      count: 0,
    };
  }

  return {
    page,
    size,
    count: 0,
    sort: 'updated_time',
    order: 'desc',
  };
}
