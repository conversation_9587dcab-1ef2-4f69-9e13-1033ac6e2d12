'use client';

import React from 'react';

interface ToolCallInfo {
  id: string;
  name: string;
  args: string;
  status: 'calling' | 'completed';
}

interface ToolCallStatusProps {
  isRunning: boolean;
  activeToolCalls: Map<string, ToolCallInfo>;
  toolCallsArray: ToolCallInfo[];
}

/**
 * ToolCallStatus - 显示工具调用状态的组件
 * 用于展示AI的工具调用过程和状态
 */
export default function ToolCallStatus({
  isRunning,
  activeToolCalls,
  toolCallsArray,
}: ToolCallStatusProps) {
  // 如果没有运行状态和工具调用，不显示
  if (!isRunning && toolCallsArray.length === 0) {
    return null;
  }

  return (
    <div className="bg-layer-2 border border-border-light rounded-lg p-4 mb-4">
      <div className="flex items-center gap-2 mb-3">
        <div className="flex items-center gap-2">
          {isRunning && (
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
          )}
          <span className="text-sm font-medium text-text-secondary">
            {isRunning ? 'AI正在思考...' : '工具调用完成'}
          </span>
        </div>
      </div>

      {toolCallsArray.length > 0 && (
        <div className="space-y-2">
          {toolCallsArray.map(toolCall => (
            <div
              key={toolCall.id}
              className="bg-layer-1 rounded-md p-3 border border-border-light"
            >
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-text-primary">
                  工具调用: {toolCall.name}
                </span>
                <span
                  className={`text-xs px-2 py-1 rounded-full ${
                    toolCall.status === 'calling'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-green-100 text-green-800'
                  }`}
                >
                  {toolCall.status === 'calling' ? '调用中' : '完成'}
                </span>
              </div>

              {toolCall.args && (
                <div className="text-xs text-text-muted">
                  <div className="font-medium mb-1">参数:</div>
                  <pre className="bg-layer-3 p-2 rounded text-xs overflow-x-auto">
                    {toolCall.args.length > 200
                      ? toolCall.args.slice(0, 200) + '...'
                      : toolCall.args}
                  </pre>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
