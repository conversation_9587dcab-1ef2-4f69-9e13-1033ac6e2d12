/**
 * 全局 Axios 错误拦截器
 * 统一捕获和处理所有 API 请求错误，自动触发全局错误处理机制
 */

import { ErrorSeverity, ErrorType } from '@/providers/GlobalErrorProvider';
import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

// 扩展 AxiosRequestConfig 类型以支持自定义属性
declare module 'axios' {
  interface InternalAxiosRequestConfig {
    metadata?: {
      startTime: number;
      url?: string;
      method?: string;
    };
  }
}

// 错误处理回调类型
type ErrorHandler = (error: {
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  details?: any;
  source?: string;
}) => void;

// 全局错误处理器引用
let globalErrorHandler: ErrorHandler | null = null;

// 请求队列管理
interface PendingRequest {
  id: string;
  config: AxiosRequestConfig;
  resolve: (value: any) => void;
  reject: (reason: any) => void;
  timestamp: number;
}

class RequestQueueManager {
  private pendingRequests: Map<string, PendingRequest> = new Map();
  private isAuthBlocked: boolean = false;
  private authPromise: Promise<boolean> | null = null;
  private maxPendingTime: number = 5 * 60 * 1000; // 5分钟超时

  /**
   * 设置认证阻塞状态
   */
  setAuthBlocked(blocked: boolean, authPromise?: Promise<boolean>): void {
    console.log(`🔐 认证状态变更: ${blocked ? '阻塞' : '解除阻塞'}`);
    this.isAuthBlocked = blocked;

    if (blocked && authPromise) {
      this.authPromise = authPromise;
      // 监听认证完成
      authPromise
        .then(success => {
          if (success) {
            this.retryPendingRequests();
          } else {
            this.clearPendingRequests('认证失败');
          }
        })
        .catch(() => {
          this.clearPendingRequests('认证过程出错');
        });
    } else if (!blocked) {
      this.authPromise = null;
    }
  }

  /**
   * 检查是否应该暂停请求
   */
  shouldBlockRequest(config: AxiosRequestConfig): boolean {
    // 检查是否为登录相关请求，这些请求不应该被阻塞
    const isAuthRequest = this.isAuthRelatedRequest(config);
    return this.isAuthBlocked && !isAuthRequest;
  }

  /**
   * 添加到待处理队列
   */
  addPendingRequest(config: AxiosRequestConfig): Promise<any> {
    return new Promise((resolve, reject) => {
      const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      const pendingRequest: PendingRequest = {
        id: requestId,
        config,
        resolve,
        reject,
        timestamp: Date.now(),
      };

      this.pendingRequests.set(requestId, pendingRequest);
      console.log(
        `⏳ 请求已暂停: ${config.method?.toUpperCase()} ${config.url}`
      );

      // 设置超时清理
      setTimeout(() => {
        if (this.pendingRequests.has(requestId)) {
          this.pendingRequests.delete(requestId);
          reject(new Error('请求等待超时'));
        }
      }, this.maxPendingTime);
    });
  }

  /**
   * 重试所有待处理的请求
   */
  private async retryPendingRequests(): Promise<void> {
    console.log(`🔄 开始重试 ${this.pendingRequests.size} 个待处理请求`);

    const requests = Array.from(this.pendingRequests.values());
    this.pendingRequests.clear();

    for (const request of requests) {
      try {
        // 重新获取token并更新请求头
        const token = getAuthToken();
        if (token && request.config.headers) {
          request.config.headers.Authorization = `Bearer ${token}`;
        }
        console.log('retry request token', token);

        // 使用axios直接发送请求，避免循环拦截
        const response = await axios.request(request.config);
        request.resolve(response);
      } catch (error) {
        request.reject(error);
      }
    }
  }

  /**
   * 清理所有待处理的请求
   */
  private clearPendingRequests(reason: string): void {
    console.log(`🗑️ 清理 ${this.pendingRequests.size} 个待处理请求: ${reason}`);

    const requests = Array.from(this.pendingRequests.values());
    this.pendingRequests.clear();

    requests.forEach(request => {
      request.reject(new Error(reason));
    });
  }

  /**
   * 检查是否为认证相关请求
   */
  private isAuthRelatedRequest(config: AxiosRequestConfig): boolean {
    const url = config.url?.toLowerCase() || '';
    const authPaths = ['/auth', '/login', '/logout', '/token', '/authconfig'];
    return authPaths.some(path => url.includes(path));
  }

  /**
   * 获取队列状态
   */
  getQueueStatus(): { isPaused: boolean; pendingCount: number } {
    return {
      isPaused: this.isAuthBlocked,
      pendingCount: this.pendingRequests.size,
    };
  }
}

// 全局请求队列管理器实例
const requestQueueManager = new RequestQueueManager();

/**
 * 设置全局错误处理器
 * 在应用启动时由 GlobalErrorProvider 调用
 */
export function setGlobalErrorHandler(handler: ErrorHandler): void {
  globalErrorHandler = handler;
}

/**
 * 创建带有错误拦截器的 Axios 实例
 */
export function createAxiosWithErrorInterceptor(
  baseConfig: AxiosRequestConfig = {}
) {
  const instance = axios.create(baseConfig);

  // 请求拦截器 - 添加认证信息和队列管理
  instance.interceptors.request.use(
    async config => {
      // 检查是否应该暂停请求
      if (requestQueueManager.shouldBlockRequest(config)) {
        console.log(
          `⏸️ 请求被暂停等待认证: ${config.method?.toUpperCase()} ${config.url}`
        );
        // 将请求加入队列，等待认证完成
        return await requestQueueManager.addPendingRequest(config);
      }

      // 动态获取最新的token
      const token = getAuthToken();
      if (token && config.headers) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      console.log(`request url: ${config.url} token: ${token}`);

      // 添加请求标识，用于错误追踪
      config.metadata = {
        startTime: Date.now(),
        url: config.url,
        method: config.method?.toUpperCase(),
      };

      return config;
    },
    error => {
      console.error('请求拦截器错误:', error);
      return Promise.reject(error);
    }
  );

  // 响应拦截器 - 处理错误
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      // 成功响应直接返回
      return response;
    },
    (error: AxiosError) => {
      // 统一错误处理
      handleAxiosError(error);
      return Promise.reject(error);
    }
  );

  return instance;
}

/**
 * 处理 Axios 错误
 */
export function handleAxiosError(error: AxiosError): void {
  if (!globalErrorHandler) {
    // 如果没有设置全局错误处理器，只打印日志
    console.error('Axios错误（未设置全局处理器）:', error);
    return;
  }

  const request = error.config;
  const response = error.response;

  // 构建错误源信息
  const source = request
    ? `${request.method?.toUpperCase()} ${request.url}`
    : '未知请求';

  // 根据错误类型进行分类处理
  if (response) {
    // 服务器响应了错误状态码
    handleResponseError(response, source);
  } else if (error.request) {
    // 请求发送了但没有收到响应
    handleNetworkError(error, source);
  } else {
    // 请求配置错误
    handleRequestConfigError(error, source);
  }
}

/**
 * 处理服务器响应错误
 */
function handleResponseError(response: AxiosResponse, source: string): void {
  const status = response.status;
  const data = response.data;

  switch (status) {
    case 401:
      // 认证失败 - 启动请求队列管理
      console.log('🔐 检测到401错误，启动请求阻塞机制');

      globalErrorHandler!({
        type: ErrorType.AUTH,
        severity: ErrorSeverity.HIGH,
        message: data?.message || '认证失败，请重新登录',
        details: {
          status,
          data,
          timestamp: Date.now(),
          shouldBlockRequests: true, // 标记需要阻塞请求
        },
        source,
      });
      break;

    case 403:
      // 权限不足
      globalErrorHandler!({
        type: ErrorType.PERMISSION,
        severity: ErrorSeverity.HIGH,
        message: data?.message || '权限不足，无法执行此操作',
        details: {
          status,
          data,
          timestamp: Date.now(),
        },
        source,
      });
      break;

    case 404:
      // 资源不存在
      globalErrorHandler!({
        type: ErrorType.SERVER,
        severity: ErrorSeverity.MEDIUM,
        message: data?.message || '请求的资源不存在',
        details: {
          status,
          data,
          timestamp: Date.now(),
        },
        source,
      });
      break;

    case 422:
      // 数据验证失败
      globalErrorHandler!({
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.MEDIUM,
        message: data?.message || '提交的数据格式不正确',
        details: {
          status,
          data,
          timestamp: Date.now(),
          validationErrors: data?.errors || data?.details,
        },
        source,
      });
      break;

    case 429:
      // 请求频率限制
      globalErrorHandler!({
        type: ErrorType.SERVER,
        severity: ErrorSeverity.MEDIUM,
        message: '请求过于频繁，请稍后再试',
        details: {
          status,
          data,
          timestamp: Date.now(),
          retryAfter: response.headers['retry-after'],
        },
        source,
      });
      break;

    case 500:
    case 502:
    case 503:
    case 504:
      // 服务器错误
      globalErrorHandler!({
        type: ErrorType.SERVER,
        severity: status >= 500 ? ErrorSeverity.HIGH : ErrorSeverity.MEDIUM,
        message: data?.message || '服务器暂时无法处理请求，请稍后重试',
        details: {
          status,
          data,
          timestamp: Date.now(),
        },
        source,
      });
      break;

    default:
      // 其他HTTP错误
      globalErrorHandler!({
        type: ErrorType.SERVER,
        severity: status >= 400 ? ErrorSeverity.MEDIUM : ErrorSeverity.LOW,
        message: data?.message || `请求失败 (${status})`,
        details: {
          status,
          data,
          timestamp: Date.now(),
        },
        source,
      });
  }
}

/**
 * 处理网络错误
 */
function handleNetworkError(error: AxiosError, source: string): void {
  if (error.code === 'ECONNABORTED') {
    // 请求超时
    globalErrorHandler!({
      type: ErrorType.TIMEOUT,
      severity: ErrorSeverity.MEDIUM,
      message: '请求超时，请检查网络连接后重试',
      details: {
        code: error.code,
        message: error.message,
        timestamp: Date.now(),
      },
      source,
    });
  } else {
    // 网络连接失败
    globalErrorHandler!({
      type: ErrorType.NETWORK,
      severity: ErrorSeverity.HIGH,
      message: '网络连接失败，请检查网络状态',
      details: {
        code: error.code,
        message: error.message,
        timestamp: Date.now(),
      },
      source,
    });
  }
}

/**
 * 处理请求配置错误
 */
function handleRequestConfigError(error: AxiosError, source: string): void {
  globalErrorHandler!({
    type: ErrorType.UNKNOWN,
    severity: ErrorSeverity.MEDIUM,
    message: '请求配置错误',
    details: {
      message: error.message,
      stack: error.stack,
      timestamp: Date.now(),
    },
    source,
  });
}

/**
 * 获取当前认证token
 * 支持多种token存储方式
 */
export function getAuthToken(): string | null {
  // 优先从localStorage获取
  try {
    const token = localStorage.getItem('authToken');
    if (token) {
      return token.replace(/"/g, '').replace(/'/g, ''); // 清理多余的引号
    }
  } catch (error) {
    console.warn('无法从localStorage获取token:', error);
  }

  // 可以添加其他token获取方式
  // 例如从cookie、sessionStorage等获取

  return null;
}

/**
 * 创建带有错误处理的API客户端
 * 这是推荐的创建API客户端的方式
 */
export function createApiClient(baseURL: string = '/') {
  return createAxiosWithErrorInterceptor({
    baseURL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 默认API客户端实例
 */
export const apiClient = createApiClient();

/**
 * 用于忽略特定错误的请求标记
 * 在请求配置中添加此标记可以跳过全局错误处理
 */
export const IGNORE_GLOBAL_ERROR = Symbol('IGNORE_GLOBAL_ERROR');

/**
 * 创建忽略全局错误处理的请求配置
 */
export function withIgnoreGlobalError<T extends AxiosRequestConfig>(
  config: T
): T {
  return {
    ...config,
    [IGNORE_GLOBAL_ERROR]: true,
  };
}

/**
 * 检查请求是否应该忽略全局错误处理
 */
export function shouldIgnoreGlobalError(config: AxiosRequestConfig): boolean {
  return !!(config as any)[IGNORE_GLOBAL_ERROR];
}

/**
 * 设置认证阻塞状态
 * 由 GlobalErrorProvider 调用，控制请求队列
 */
export function setAuthBlocked(
  blocked: boolean,
  authPromise?: Promise<boolean>
): void {
  requestQueueManager.setAuthBlocked(blocked, authPromise);
}

/**
 * 获取请求队列状态
 * 用于UI显示当前的请求状态
 */
export function getRequestQueueStatus(): {
  isPaused: boolean;
  pendingCount: number;
} {
  return requestQueueManager.getQueueStatus();
}
