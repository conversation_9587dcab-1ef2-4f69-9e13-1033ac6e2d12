import { Employee } from '@/types/employee';
import { createApiClient } from '@/lib/http/errorInterceptor';

const api = createApiClient();

/**
 * 获取AI员工列表或按姓名查询，支持分页
 * @param params.Disabled 是否禁用，传入false表示只查询未禁用的员工
 * @param params.IsPublic 是否公开，true表示公开(共享)，false表示个人
 */
export async function getEmployees(params?: {
  Name?: string;
  Disabled?: boolean | null;
  'Pager.Page'?: number;
  'Pager.Size'?: number;
  Tags?: string;
  'Pager.Sort'?: string;
  'Pager.Order'?: string;
  IsPublic?: boolean;
  ExcludePersonalCreated?: boolean;
}) {
  const response = await api.get('/api/employees', { params });
  return response.data;
}

/**
 * 获取员工标签列表
 * @returns 标签列表
 */
export async function getEmployeeTags() {
  const response = await api.get('/api/employee/tags');
  return response.data;
}

/**
 * 启用AI员工
 * @param employeeId 员工ID
 * @returns 操作结果
 */
export async function enableEmployee(employeeId: string) {
  const response = await api.put(`/api/employee/${employeeId}/enable`);
  return response.data;
}

/**
 * 禁用AI员工
 * @param employeeId 员工ID
 * @returns 操作结果
 */
export async function disableEmployee(employeeId: string) {
  const response = await api.put(`/api/employee/${employeeId}/disable`);
  return response.data;
}

/**
 * 删除AI员工
 * @param employeeId 员工ID
 * @returns 操作结果
 */
export async function deleteEmployee(employeeId: string) {
  const response = await api.delete(`/api/employee/${employeeId}`);
  return response.data;
}

/**
 * 创建AI员工
 * @param data 员工数据
 * @returns 创建的员工信息
 */
export async function createEmployee(data: {
  name: string;
  description: string;
  tags?: string[];
  isPublic?: boolean;
  agentId?: string;
}) {
  const response = await api.post('/api/employee', data);
  return response.data;
}

/**
 * 获取单个AI员工详情
 * @param employeeId 员工ID
 * @returns 员工详情
 */
export async function getEmployeeDetail(employeeId: string) {
  const response = await api.get(`/api/employee/${employeeId}`);
  return response.data;
}

/**
 * 更新AI员工
 * @param employeeId 员工ID
 * @param data 更新数据
 * @returns 更新后的员工信息
 */
export async function updateEmployee(
  employeeId: string,
  data: {
    name?: string;
    description?: string;
    tags?: string[];
    isPublic?: boolean;
    agentId?: string | null;
    disabled?: boolean;
  }
) {
  const response = await api.put(`/api/employee/${employeeId}`, data);
  return response.data;
}

/**
 * 管理端获取AI员工列表，支持多条件筛选
 * @param params 查询参数，详见接口文档
 * @returns 员工列表及分页信息
 */
export async function getManageEmployees(params?: {
  EmployeeIds?: string[] | null;
  Name?: string | null;
  IsPublic?: boolean | null;
  Disabled?: boolean | null;
  Tags?: string[] | null;
  CreateUserId?: string | null;
  AgentId?: string | null;
  'Pager.Page'?: number;
  'Pager.Size'?: number;
  'Pager.Sort'?: string | null;
  'Pager.Order'?: string | null;
  'Pager.Offset'?: number;
  'Pager.ReturnTotal'?: boolean;
}) {
  const response = await api.get('/api/employee/manage-employees', { params });
  return response.data;
}

/**
 * 获取知识库关联的AI员工列表
 * @param knowledgeId 知识库ID
 * @returns AI员工列表
 */
export async function getKnowledgeAssociatedEmployees(knowledgeId: string) {
  const response = await api.get(
    `/api/employee/knowledge-associated-employees/${knowledgeId}`
  );
  return response.data;
}
