'use client';
import React from 'react';
import { createEmployee } from '@/api/employee';
import EmployeePageLayout, {
  EmployeeFormValues,
} from '@/components/ai-staff/EmployeePageLayout';

export default function CreateEmployeePage() {
  const handleCreate = async (values: EmployeeFormValues) => {
    await createEmployee(values);
  };
  return (
    <EmployeePageLayout
      mode="create"
      initialValues={{}}
      onSubmit={handleCreate}
      submitText="创建"
    />
  );
}
