'use client';

import { X } from 'lucide-react';
import { useEffect, useState } from 'react';

interface DeleteConversationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  conversationTitle?: string;
}

export default function DeleteConversationModal({
  isOpen,
  onClose,
  onConfirm,
  conversationTitle,
}: DeleteConversationModalProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      setIsAnimating(true);
    } else {
      setIsAnimating(false);
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 200); // 等待退出动画完成
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  // 处理ESC键关闭
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // 防止背景滚动
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // 处理背景点击关闭
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && !isDeleting) {
      onClose();
    }
  };

  // 处理确认删除
  const handleConfirmDelete = async () => {
    setIsDeleting(true);
    try {
      await onConfirm();
      onClose();
    } catch (error) {
      console.error('删除失败:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  if (!isVisible) return null;

  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center transition-all duration-200 ${
        isAnimating ? 'bg-[rgba(0,0,0,0.08)]' : 'bg-black/0'
      }`}
      onClick={handleBackdropClick}
    >
      <div
        className={`bg-white rounded-xl w-[400px] shadow-lg transition-all duration-200 transform ${
          isAnimating
            ? 'scale-100 opacity-100 translate-y-0'
            : 'scale-95 opacity-0 translate-y-4'
        }`}
      >
        {/* 标题区域 */}
        <div className="flex justify-between items-center p-6 pb-4">
          <h3 className="text-lg font-medium text-text-primary">删除会话</h3>
          <button
            onClick={onClose}
            disabled={isDeleting}
            className={`w-6 h-6 flex items-center justify-center rounded-md transition-all duration-150 cursor-pointer transform hover:scale-110 active:scale-95 ${
              isDeleting ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'
            }`}
          >
            <X
              className={`w-4 h-4 transition-colors duration-150 ${
                isDeleting
                  ? 'text-gray-400'
                  : 'text-text-muted hover:text-text-primary'
              }`}
            />
          </button>
        </div>

        {/* 内容区域 */}
        <div className="px-6 pb-4">
          <p className="text-sm text-text-secondary">
            会话记录将被删除无法恢复
          </p>
        </div>

        {/* 按钮区域 */}
        <div className="flex justify-end gap-3 px-6 pb-6">
          <button
            onClick={onClose}
            disabled={isDeleting}
            className={`px-4 py-2 text-sm font-medium text-text-secondary rounded-lg transition-all duration-150 cursor-pointer transform hover:scale-105 active:scale-95 ${
              isDeleting
                ? 'bg-gray-50 text-gray-400 cursor-not-allowed'
                : 'bg-gray-100 hover:bg-gray-200'
            }`}
          >
            取消
          </button>
          <button
            onClick={handleConfirmDelete}
            disabled={isDeleting}
            className={`px-4 py-2 text-sm font-medium text-white rounded-lg transition-all duration-150 cursor-pointer transform hover:scale-105 active:scale-95 flex items-center gap-2 ${
              isDeleting
                ? 'bg-red-400 cursor-not-allowed'
                : 'bg-red-500 hover:bg-red-600'
            }`}
          >
            {isDeleting && (
              <svg
                className="animate-spin h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
            )}
            {isDeleting ? '删除中...' : '确认删除'}
          </button>
        </div>
      </div>
    </div>
  );
}
