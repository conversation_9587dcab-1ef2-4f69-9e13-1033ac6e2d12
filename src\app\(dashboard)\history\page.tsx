'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useConversations } from '@/components/history/hooks/useConversations';
import { useHistoryContext } from '@/components/history/HistoryProvider';

export default function HistoryPage() {
  const router = useRouter();
  const { userId } = useHistoryContext();
  const { conversations, loading, loadConversations } = useConversations();

  // 自动重定向到最新会话
  useEffect(() => {
    // 如果还在加载，等待加载完成
    if (loading) return;

    // 如果没有会话，先加载会话列表
    if (conversations.length === 0) {
      loadConversations(userId);
      return;
    }

    // 如果有会话，重定向到最新的会话
    if (conversations.length > 0) {
      const latestConversation = conversations[0];
      router.replace(`/history/${latestConversation.id}`);
    }
  }, [conversations, loading, userId, loadConversations, router]);

  // 显示加载状态或选择会话的提示
  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center space-y-4">
        {loading ? (
          <>
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <div className="text-text-muted text-sm">加载会话列表中...</div>
          </>
        ) : (
          <>
            <svg
              className="w-16 h-16 mx-auto text-text-muted"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
              />
            </svg>
            <div>
              <div className="text-text-muted text-sm">
                请选择一个会话开始查看
              </div>
              <div className="text-text-muted text-xs mt-1">
                从左侧会话列表中选择一个会话
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
