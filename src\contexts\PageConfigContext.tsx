'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { PageConfig, PageConfigSetter } from '@/types/pageConfig';

/**
 * 页面配置 Context 值类型
 */
interface PageConfigContextValue {
  /** 当前页面配置 */
  config: PageConfig;
  /** 设置页面配置 */
  setConfig: PageConfigSetter;
  /** 重置页面配置 */
  resetConfig: () => void;
}

/**
 * 默认页面配置
 */
const defaultConfig: PageConfig = {
  breadcrumb: {
    title: '加载中...',
    loading: true,
  },
  title: '',
  actions: [],
  meta: {},
};

/**
 * 页面配置 Context
 */
const PageConfigContext = createContext<PageConfigContextValue | undefined>(
  undefined
);

/**
 * 页面配置 Provider Props
 */
interface PageConfigProviderProps {
  children: ReactNode;
}

/**
 * 页面配置 Provider
 */
export function PageConfigProvider({ children }: PageConfigProviderProps) {
  const [config, setConfigState] = useState<PageConfig>(defaultConfig);

  /**
   * 设置页面配置（支持部分更新）
   */
  const setConfig: PageConfigSetter = newConfig => {
    setConfigState(prevConfig => ({
      ...prevConfig,
      ...newConfig,
      // 深度合并面包屑配置
      breadcrumb: newConfig.breadcrumb
        ? {
            ...prevConfig.breadcrumb,
            ...newConfig.breadcrumb,
          }
        : prevConfig.breadcrumb,
      // 深度合并元数据
      meta: newConfig.meta
        ? {
            ...prevConfig.meta,
            ...newConfig.meta,
          }
        : prevConfig.meta,
    }));
  };

  /**
   * 重置页面配置
   */
  const resetConfig = () => {
    setConfigState(defaultConfig);
  };

  const value: PageConfigContextValue = {
    config,
    setConfig,
    resetConfig,
  };

  return (
    <PageConfigContext.Provider value={value}>
      {children}
    </PageConfigContext.Provider>
  );
}

/**
 * 使用页面配置 Context Hook
 */
export function usePageConfigContext(): PageConfigContextValue {
  const context = useContext(PageConfigContext);

  if (context === undefined) {
    throw new Error(
      'usePageConfigContext must be used within a PageConfigProvider'
    );
  }

  return context;
}
