import { useSetModalState } from '@/hooks/common-hooks';
import {
  useCreateNextDocument,
  useNextWebCrawl,
  useRunNextDocument,
  useSaveNextDocumentName,
  useSetDocumentMeta,
  useSetNextDocumentParser,
} from '@/hooks/document-hooks';
import { useGetKnowledgeSearchParams } from '@/hooks/route-hook';
import { IDocumentInfo } from '@/interfaces/document';
import { IChangeParserConfigRequestBody } from '@/interfaces/request/document';
import { TableRowSelection } from 'antd/es/table/interface';
import { useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';
import { useAddFileToKnowledge } from '../_hooks/knowledge-hooks';

export const useGetRowSelection = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showSelection, setShowSelection] = useState<boolean>(false);

  const rowSelection: TableRowSelection<IDocumentInfo> = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  return {
    rowSelection,
    showSelection,
    setShowSelection,
  };
};

export const useHandleWebCrawl = () => {
  const {
    visible: webCrawlUploadVisible,
    hideModal: hideWebCrawlUploadModal,
    showModal: showWebCrawlUploadModal,
  } = useSetModalState();
  const { webCrawl, loading } = useNextWebCrawl();

  const onWebCrawlUploadOk = useCallback(
    async (name: string, url: string) => {
      const ret = await webCrawl({ name, url });
      if (ret === 0) {
        hideWebCrawlUploadModal();
        return 0;
      }
      return -1;
    },
    [webCrawl, hideWebCrawlUploadModal]
  );

  return {
    webCrawlUploadLoading: loading,
    onWebCrawlUploadOk,
    webCrawlUploadVisible,
    hideWebCrawlUploadModal,
    showWebCrawlUploadModal,
  };
};

export const useCreateEmptyDocument = () => {
  const { createDocument, loading } = useCreateNextDocument();

  const {
    visible: createVisible,
    hideModal: hideCreateModal,
    showModal: showCreateModal,
  } = useSetModalState();

  const onCreateOk = useCallback(
    async (name: string) => {
      const ret = await createDocument(name);
      if (ret === 0) {
        hideCreateModal();
      }
    },
    [hideCreateModal, createDocument]
  );

  return {
    createLoading: loading,
    onCreateOk,
    createVisible,
    hideCreateModal,
    showCreateModal,
  };
};

export const useHandleRunDocumentByIds = (id: string) => {
  const { runDocumentByIds, loading } = useRunNextDocument();
  const [currentId, setCurrentId] = useState<string>('');
  const isLoading = loading && currentId !== '' && currentId === id;

  const handleRunDocumentByIds = async (
    documentId: string,
    isRunning: boolean,
    parseConfig: IChangeParserConfigRequestBody,
    embed = false,
    shouldDelete = false
  ) => {
    if (isLoading) {
      return;
    }
    setCurrentId(documentId);
    try {
      await runDocumentByIds({
        documentIds: [documentId],
        run: isRunning ? 2 : 1, // 运行标签(1:运行2取消)
        shouldDelete,
        parseConfig: parseConfig,
        embed: embed,
      });
      setCurrentId('');
    } catch (error) {
      setCurrentId('');
    }
  };

  return {
    handleRunDocumentByIds,
    loading: isLoading,
  };
};

export const useNavigateToOtherPage = () => {
  const router = useRouter();
  const { knowledgeId } = useGetKnowledgeSearchParams();

  const toChunk = useCallback(
    (
      id: string,
      {
        page,
        pageSize,
        parser_id,
      }: { page: number; pageSize: number; parser_id: string }
    ) => {
      router.push(
        `/knowledge/dataset/chunk?id=${knowledgeId}&doc_id=${id}&doc_page=${page}&doc_page_size=${pageSize}&parser_id=${parser_id}`
      );
    },
    [router, knowledgeId]
  );

  return { toChunk };
};

export const useRenameDocument = (documentId: string) => {
  const { saveName, loading } = useSaveNextDocumentName();

  const {
    visible: renameVisible,
    hideModal: hideRenameModal,
    showModal: showRenameModal,
  } = useSetModalState();

  const onRenameOk = useCallback(
    async (name: string) => {
      const ret = await saveName({ documentId, name });
      if (ret === 0) {
        hideRenameModal();
      }
    },
    [hideRenameModal, saveName, documentId]
  );

  return {
    renameLoading: loading,
    onRenameOk,
    renameVisible,
    hideRenameModal,
    showRenameModal,
  };
};

export const useChangeDocumentParser = (documentId: string) => {
  const { setDocumentParser, loading } = useSetNextDocumentParser();

  const {
    visible: changeParserVisible,
    hideModal: hideChangeParserModal,
    showModal: showChangeParserModal,
  } = useSetModalState();

  const onChangeParserOk = useCallback(
    async (parserId: string, parserConfig: IChangeParserConfigRequestBody) => {
      const ret = await setDocumentParser({
        parserId,
        documentId,
        parserConfig,
      });
      if (ret === 0) {
        hideChangeParserModal();
      }
    },
    [hideChangeParserModal, setDocumentParser, documentId]
  );

  return {
    changeParserLoading: loading,
    onChangeParserOk,
    changeParserVisible,
    hideChangeParserModal,
    showChangeParserModal,
  };
};

export const useShowMetaModal = (documentId: string) => {
  const { setDocumentMeta, loading } = useSetDocumentMeta();

  const {
    visible: setMetaVisible,
    hideModal: hideSetMetaModal,
    showModal: showSetMetaModal,
  } = useSetModalState();

  const onSetMetaModalOk = useCallback(
    async (meta: string) => {
      const ret = await setDocumentMeta({
        documentId,
        meta,
      });
      if (ret === 0) {
        hideSetMetaModal();
      }
    },
    [setDocumentMeta, documentId, hideSetMetaModal]
  );

  return {
    setMetaLoading: loading,
    onSetMetaModalOk,
    setMetaVisible,
    hideSetMetaModal,
    showSetMetaModal,
  };
};

export const useHandleAddFileToKnowledge = () => {
  const { knowledgeId } = useGetKnowledgeSearchParams();
  const {
    visible: addFileToKnowledgeVisible,
    hideModal: hideAddFileToKnowledgeModal,
    showModal: showAddFileToKnowledgeModal,
  } = useSetModalState();
  const { addFileToKnowledge, loading } = useAddFileToKnowledge();

  const onAddFileToKnowledgeOk = useCallback(
    async (fileIds: string[]) => {
      const ret = await addFileToKnowledge({
        fileIds: fileIds,
        kbIds: [knowledgeId],
      });

      if (ret === 0) {
        hideAddFileToKnowledgeModal();
      }
      return ret;
    },
    [addFileToKnowledge, hideAddFileToKnowledgeModal, knowledgeId]
  );

  const handleShowAddFileToKnowledgeModal = useCallback(() => {
    showAddFileToKnowledgeModal();
  }, [showAddFileToKnowledgeModal]);

  return {
    addFileToKnowledgeLoading: loading,
    onAddFileToKnowledgeOk,
    addFileToKnowledgeVisible,
    hideAddFileToKnowledgeModal,
    showAddFileToKnowledgeModal: handleShowAddFileToKnowledgeModal,
  };
};
