import { getKnowledgeAssociatedEmployees } from '@/api/employee';
import { Employee } from '@/types/employee';
import { CloseOutlined, UserOutlined } from '@ant-design/icons';
import { Button, Modal } from 'antd';
import { forwardRef, useImperativeHandle, useState } from 'react';
import { useDeleteKnowledge } from '../_hooks/knowledge-hooks';

interface DeleteKnowledgeModalProps {
  onSuccess?: () => void;
}

export interface DeleteKnowledgeModalRef {
  open: (knowledge: any) => void;
  close: () => void;
}

const DeleteKnowledgeModal = forwardRef<
  DeleteKnowledgeModalRef,
  DeleteKnowledgeModalProps
>(({ onSuccess }, ref) => {
  const [visible, setVisible] = useState(false);
  const [knowledge, setKnowledge] = useState<any>(null);
  const [associatedEmployees, setAssociatedEmployees] = useState<Employee[]>(
    []
  );
  const [loading, setLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const { deleteKnowledge } = useDeleteKnowledge();

  useImperativeHandle(ref, () => ({
    open: (knowledgeData: any) => {
      setKnowledge(knowledgeData);
      setVisible(true);
      loadAssociatedEmployees(knowledgeData.id);
    },
    close: () => {
      handleCancel();
    },
  }));

  const loadAssociatedEmployees = async (knowledgeId: string) => {
    setLoading(true);
    try {
      const response = await getKnowledgeAssociatedEmployees(knowledgeId);
      setAssociatedEmployees(response.data || []);
    } catch (error) {
      console.error('获取关联员工失败:', error);
      setAssociatedEmployees([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setVisible(false);
    setKnowledge(null);
    setAssociatedEmployees([]);
  };

  const handleConfirm = async () => {
    if (!knowledge) return;

    setDeleteLoading(true);
    try {
      await deleteKnowledge(knowledge.id);
      handleCancel();
      onSuccess?.();
    } catch (error) {
      console.error('删除知识库失败:', error);
    } finally {
      setDeleteLoading(false);
    }
  };

  return (
    <Modal
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={600}
      centered
      closeIcon={<CloseOutlined />}
      className="delete-knowledge-modal"
    >
      <div className="p-6">
        {/* 标题 */}
        <div className="text-xl font-medium mb-6">删除知识库</div>

        {/* 提示信息 */}
        <div className="mb-6 text-gray-600">
          “{knowledge?.name}” 正在被 {associatedEmployees.length}{' '}
          个AI员工使用，删除将断开关联
        </div>

        {/* 员工列表 */}
        <div className="mb-8 max-h-60 overflow-y-auto">
          {loading ? (
            <div className="text-center py-4 text-gray-500">加载中...</div>
          ) : associatedEmployees.length > 0 ? (
            <div className="space-y-3">
              {associatedEmployees.map(employee => (
                <div
                  key={employee.id}
                  className="flex items-center space-x-3 py-2"
                >
                  <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                    {employee.avatar ? (
                      <img
                        src={employee.avatar}
                        alt={employee.name}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                    ) : (
                      <UserOutlined className="text-gray-400" />
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">
                      {employee.name}
                    </div>
                    {employee.role && (
                      <div className="text-sm text-gray-500">
                        {employee.role}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4 text-gray-500">
              暂无关联的AI员工
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3">
          <Button onClick={handleCancel} size="large">
            取消
          </Button>
          <Button
            type="primary"
            danger
            onClick={handleConfirm}
            loading={deleteLoading}
            size="large"
          >
            确认删除
          </Button>
        </div>
      </div>
    </Modal>
  );
});

DeleteKnowledgeModal.displayName = 'DeleteKnowledgeModal';

export default DeleteKnowledgeModal;
