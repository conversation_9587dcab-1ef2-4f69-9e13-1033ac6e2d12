'use client';

import { useHistoryContext } from '@/components/history/HistoryProvider';
import { useChatMessages } from '@/components/history/hooks/useChatMessages';
import { useRouteSync } from '@/components/history/hooks/useRouteSync';
import { ChatMessage } from '@/lib/chat-engine/types';
import React, { useCallback, useEffect, useRef } from 'react';
import ToolCallStatus from './ToolCallStatus';

/**
 * 完全解耦的消息列表组件
 * 只负责消息显示，通过事件总线与其他组件通信
 */
export default function MessageList() {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // 获取当前会话ID
  const { userId } = useHistoryContext();
  const { currentConversationId } = useRouteSync(userId);

  // 聊天引擎状态（只读）
  const {
    messages,
    loading,
    isStreaming,
    streamingMessageId,
    isRunning,
    activeToolCalls,
    toolCallsArray,
  } = useChatMessages(currentConversationId || undefined);

  // 处理复制消息
  const handleCopyMessage = useCallback(async (content: string) => {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(content);
      } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = content;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
      }
      // 可以添加复制成功提示
    } catch (error) {
      console.error('复制失败:', error);
    }
  }, []);

  // 自动滚动到底部
  const scrollToBottom = useCallback(() => {
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  }, []);

  // 当消息更新时滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [messages.length, isStreaming, scrollToBottom]);

  // 监听流式消息内容变化，实时滚动
  useEffect(() => {
    if (isStreaming && streamingMessageId && messages.length > 0) {
      const streamingMessage = messages.find(
        msg => msg.id === streamingMessageId
      );
      if (streamingMessage?.content) {
        scrollToBottom();
      }
    }
  }, [messages, isStreaming, streamingMessageId, scrollToBottom]);

  // 渲染Markdown内容
  const renderMarkdownContent = useCallback((content: string) => {
    if (!content) return null;
    // 简单的markdown渲染，支持基础格式
    const lines = content.split('\n');
    const elements: React.ReactNode[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // 处理标题
      if (line.startsWith('# ')) {
        elements.push(
          <h1 key={i} className="text-xl font-medium text-text-primary mb-3">
            {line.slice(2)}
          </h1>
        );
      } else if (line.startsWith('## ')) {
        elements.push(
          <h2 key={i} className="text-lg font-medium text-text-primary mb-2">
            {line.slice(3)}
          </h2>
        );
      } else if (line.startsWith('### ')) {
        elements.push(
          <h3
            key={i}
            className="text-base font-medium text-text-secondary mb-2"
          >
            {line.slice(4)}
          </h3>
        );
      } else if (line.startsWith('【') && line.includes('】')) {
        // 处理分类标签
        elements.push(
          <div
            key={i}
            className="text-base font-medium text-text-secondary mb-1"
          >
            {line}
          </div>
        );
      } else if (line.trim() === '') {
        // 空行
        elements.push(<div key={i} className="h-1" />);
      } else {
        // 普通文本，支持粗体
        const processedLine = line.replace(
          /\*\*(.*?)\*\*/g,
          '<strong>$1</strong>'
        );
        elements.push(
          <div
            key={i}
            className="text-base leading-8 text-text-primary"
            dangerouslySetInnerHTML={{ __html: processedLine }}
          />
        );
      }
    }

    return elements;
  }, []);

  // 渲染单个消息
  const renderMessage = useCallback(
    (message: ChatMessage, index: number) => {
      const isUserMessage = ['user', 'client', 'admin'].includes(message.role);
      const isStreamingMessage =
        message.isStreaming || streamingMessageId === message.id;

      if (isUserMessage) {
        // 用户消息样式
        return (
          <div
            key={`${message.id}-${index}-${message.timestamp}`}
            className="flex flex-col items-end gap-1 group"
          >
            <div className="bg-layer-3 rounded-lg px-3 py-2 max-w-[465px]">
              <div className="text-base leading-6 text-text-primary">
                {message.content}
              </div>
            </div>
            {/* 操作按钮 - 默认隐藏，hover显示 */}
            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <button
                onClick={() => handleCopyMessage(message.content)}
                className="w-8 h-8 rounded-lg hover:bg-layer-2 flex items-center justify-center"
                title="复制"
              >
                <svg
                  className="w-4 h-4 text-text-muted"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.4}
                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
              </button>
            </div>
          </div>
        );
      } else {
        // 助手消息样式
        return (
          <div
            key={`${message.id}-${index}-${message.timestamp}`}
            className="flex flex-col items-start gap-1 group"
          >
            <div className="bg-transparent rounded-lg px-3 py-2 max-w-[626px]">
              <div className="space-y-3">
                {renderMarkdownContent(message.content)}
              </div>

              {/* 时间戳和状态 */}
              {(isStreamingMessage || message.status) && (
                <div className="text-xs mt-2 text-text-muted">
                  {isStreamingMessage ? (
                    <span className="inline-flex items-center">
                      <div className="w-1 h-1 bg-current rounded-full animate-pulse mr-1"></div>
                      输入中...
                    </span>
                  ) : message.status === 'pending' ? (
                    <span className="text-xs opacity-70">发送中...</span>
                  ) : message.status === 'failed' ? (
                    <span className="text-xs text-error">发送失败</span>
                  ) : null}
                </div>
              )}
            </div>

            {/* 操作按钮 - 默认隐藏，hover显示 */}
            {!isStreamingMessage && (
              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  onClick={() => handleCopyMessage(message.content)}
                  className="w-8 h-8 rounded-lg hover:bg-layer-2 flex items-center justify-center"
                  title="复制"
                >
                  <svg
                    className="w-4 h-4 text-text-muted"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.4}
                      d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                    />
                  </svg>
                </button>
              </div>
            )}
          </div>
        );
      }
    },
    [streamingMessageId, renderMarkdownContent, handleCopyMessage]
  );

  return (
    <div className="relative h-full overflow-y-auto" ref={messagesContainerRef}>
      {/* 消息内容区域 */}
      <div className="relative z-0 max-w-[960px] mx-auto px-6 py-6">
        <div className="space-y-6">
          {loading ? (
            <div className="text-center py-12 space-y-4">
              <div className="flex flex-col items-center justify-center">
                <svg
                  className="animate-spin h-6 w-6 text-text-muted mb-2"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                <div className="text-text-muted text-sm">加载中...</div>
              </div>
            </div>
          ) : (
            <>
              {/* 工具调用状态显示 */}
              <ToolCallStatus
                isRunning={isRunning}
                activeToolCalls={activeToolCalls}
                toolCallsArray={toolCallsArray}
              />
              {/* 空状态 */}
              {messages.length === 0 && (
                <div className="text-center py-12 space-y-4">
                  <svg
                    className="w-16 h-16 mx-auto text-text-muted"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1}
                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                    />
                  </svg>
                  <div className="text-center">
                    <div className="text-text-muted text-sm">
                      这个会话还没有消息
                    </div>
                    <div className="text-text-muted text-xs mt-1">
                      在下方输入框开始对话
                    </div>
                  </div>
                </div>
              )}

              {/* 消息列表 */}
              {messages.map(renderMessage)}

              {/* 滚动锚点和底部安全间距 */}
              <div ref={messagesEndRef} className="h-6" />
            </>
          )}
        </div>
      </div>
    </div>
  );
}
