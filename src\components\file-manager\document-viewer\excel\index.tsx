import FileError from '../file-error';
import { useFetchExcel } from '../hooks';

const Excel = ({ filePath }: { filePath: string }) => {
  const { status, containerRef, error } = useFetchExcel(filePath);

  return (
    <div
      id="excel"
      ref={containerRef}
      key={filePath} // 添加key属性，确保文件路径变化时重新渲染
      style={{
        height: '100%',
        width: '100%',
        overflow: 'auto',
      }}
    >
      {status ? null : <FileError>{error}</FileError>}
    </div>
  );
};

export default Excel;
