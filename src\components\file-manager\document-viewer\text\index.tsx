import { Spin } from 'antd';
import FileError from '../file-error';

import { useFetchText } from '../hooks';
// import styles from './index.module.less';

const Text = ({ filePath }: { filePath: string }) => {
  const { succeed, containerRef, error } = useFetchText(filePath);

  return (
    <>
      {succeed ? (
        <section className="textContainer">
          <div id="text" ref={containerRef}>
            <Spin />
          </div>
        </section>
      ) : (
        <FileError>{error}</FileError>
      )}
    </>
  );
};

export default Text;
