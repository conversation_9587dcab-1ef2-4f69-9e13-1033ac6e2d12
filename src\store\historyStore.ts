'use client';

import { conversationApi } from '@/services/apiManager';
import { ConversationItem } from '@/types/conversation';
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

interface ConversationGroup {
  label: string;
  conversations: ConversationItem[];
}

interface HistoryState {
  // 核心数据
  conversations: ConversationItem[];
  selectedConversationId: string | null;
  selectedAgentId: string | null;

  // 加载状态
  loading: boolean;
  error: string | null;

  // 分页状态
  currentPage: number;
  hasMore: boolean;

  // 操作状态
  isDeletingConversation: string | null;
  isRenamingConversation: string | null;
}

interface HistoryActions {
  // 数据加载
  loadConversations: (userId: string, refresh?: boolean) => Promise<void>;
  loadMoreConversations: (userId: string) => Promise<void>;

  // 会话操作
  addConversation: (conversation: ConversationItem) => void;
  deleteConversation: (
    agentId: string,
    conversationId: string
  ) => Promise<boolean>;
  renameConversation: (
    agentId: string,
    conversationId: string,
    newTitle: string
  ) => Promise<boolean>;

  // 状态管理
  setSelectedConversation: (conversationId: string | null) => void;
  setError: (error: string | null) => void;
  reset: () => void;

  // 计算属性
  getGroupedConversations: () => ConversationGroup[];
  getConversationById: (conversationId: string) => ConversationItem | null;
}

// 简单的缓存管理
class SimpleCache {
  private cache = new Map<string, { data: any; timestamp: number }>();
  private ttl = 5 * 60 * 1000; // 5分钟过期

  set(key: string, data: any) {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  get(key: string) {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  invalidate(pattern: string) {
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    }
  }
}

const cache = new SimpleCache();

export const useHistoryStore = create<HistoryState & HistoryActions>()(
  immer((set, get) => ({
    // 初始状态
    conversations: [],
    selectedConversationId: null,
    selectedAgentId: null,
    loading: false,
    error: null,
    currentPage: 1,
    hasMore: true,
    isDeletingConversation: null,
    isRenamingConversation: null,

    // 加载会话列表
    loadConversations: async (userId: string, refresh = false) => {
      const cacheKey = `conversations-${userId}-page-1`;

      // 检查缓存
      if (!refresh) {
        const cached = cache.get(cacheKey);
        if (cached) {
          set(state => {
            state.conversations = cached.data;
            state.hasMore = cached.hasMore;
            state.loading = false;
            state.error = null;
          });
          return;
        }
      }

      set(state => {
        state.loading = true;
        state.error = null;
      });

      try {
        const resp = await conversationApi.getConversations(userId, 1, 20);

        // 缓存结果
        cache.set(cacheKey, {
          data: resp.data,
          hasMore: resp.pagination?.hasMore || false,
        });

        set(state => {
          state.conversations = resp.data;
          state.hasMore = resp.pagination?.hasMore || false;
          state.currentPage = 1;
          state.loading = false;
          state.error = null;
        });
      } catch (error: any) {
        set(state => {
          state.error = error.message || '加载会话列表失败';
          state.loading = false;
        });
        throw error;
      }
    },

    // 加载更多会话
    loadMoreConversations: async (userId: string) => {
      const state = get();
      if (!state.hasMore || state.loading) return;

      const nextPage = state.currentPage + 1;
      const cacheKey = `conversations-${userId}-page-${nextPage}`;

      // 检查缓存
      const cached = cache.get(cacheKey);
      if (cached) {
        set(state => {
          state.conversations = [...state.conversations, ...cached.data];
          state.hasMore = cached.hasMore;
          state.currentPage = nextPage;
        });
        return;
      }

      set(state => {
        state.loading = true;
      });

      try {
        const resp = await conversationApi.getConversations(
          userId,
          nextPage,
          20
        );

        // 缓存结果
        cache.set(cacheKey, {
          data: resp.data,
          hasMore: resp.pagination?.hasMore || false,
        });

        set(state => {
          state.conversations = [...state.conversations, ...resp.data];
          state.hasMore = resp.pagination?.hasMore || false;
          state.currentPage = nextPage;
          state.loading = false;
        });
      } catch (error: any) {
        set(state => {
          state.error = error.message || '加载更多会话失败';
          state.loading = false;
        });
        throw error;
      }
    },

    // 添加新会话到本地状态
    addConversation: (conversation: ConversationItem) => {
      set(state => {
        // 检查会话是否已存在，避免重复添加
        const existingIndex = state.conversations.findIndex(
          c => c.id === conversation.id
        );

        if (existingIndex === -1) {
          // 新会话添加到数组开头（最新的在前面）
          state.conversations.unshift(conversation);
        } else {
          // 如果已存在，更新会话信息
          state.conversations[existingIndex] = conversation;
        }
      });

      // 清除相关缓存，确保下次加载时数据同步
      cache.invalidate('conversations-');
    },

    // 删除会话
    deleteConversation: async (agentId: string, conversationId: string) => {
      set(state => {
        state.isDeletingConversation = conversationId;
      });

      try {
        await conversationApi.deleteConversation(conversationId, agentId);

        // 乐观更新
        set(state => {
          state.conversations = state.conversations.filter(
            c => c.id !== conversationId
          );
          state.isDeletingConversation = null;

          // 如果删除的是当前选中的会话，清空选中状态
          if (state.selectedConversationId === conversationId) {
            state.selectedConversationId = null;
            state.selectedAgentId = null;
          }
        });

        // 清除相关缓存
        cache.invalidate('conversations-');

        return true;
      } catch (error: any) {
        set(state => {
          state.error = error.message || '删除会话失败';
          state.isDeletingConversation = null;
        });
        return false;
      }
    },

    // 重命名会话
    renameConversation: async (
      agentId: string,
      conversationId: string,
      newTitle: string
    ) => {
      if (!newTitle.trim()) return false;

      set(state => {
        state.isRenamingConversation = conversationId;
      });

      try {
        await conversationApi.renameConversation(
          conversationId,
          newTitle.trim(),
          agentId
        );

        // 乐观更新
        set(state => {
          state.conversations = state.conversations.map(c =>
            c.id === conversationId ? { ...c, titleAlias: newTitle.trim() } : c
          );
          state.isRenamingConversation = null;
        });

        // 清除相关缓存
        cache.invalidate('conversations-');

        return true;
      } catch (error: any) {
        set(state => {
          state.error = error.message || '重命名会话失败';
          state.isRenamingConversation = null;
        });
        return false;
      }
    },

    // 设置选中的会话
    setSelectedConversation: (conversationId: string | null) => {
      set(state => {
        state.selectedConversationId = conversationId;
        state.selectedAgentId = conversationId
          ? get().getConversationById(conversationId)?.agent_id || null
          : null;
      });
    },

    // 设置错误
    setError: (error: string | null) => {
      set(state => {
        state.error = error;
      });
    },

    // 重置状态
    reset: () => {
      set(state => {
        state.conversations = [];
        state.selectedConversationId = null;
        state.selectedAgentId = null;
        state.loading = false;
        state.error = null;
        state.currentPage = 1;
        state.hasMore = true;
        state.isDeletingConversation = null;
        state.isRenamingConversation = null;
      });
    },

    // 获取分组的会话列表
    getGroupedConversations: () => {
      const state = get();
      const groups: ConversationGroup[] = [];

      if (state.conversations.length === 0) {
        return groups;
      }

      // 按更新时间分组
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      const todayConversations: ConversationItem[] = [];
      const yesterdayConversations: ConversationItem[] = [];
      const olderConversations: { [key: string]: ConversationItem[] } = {};

      state.conversations.forEach(conv => {
        const convDate = new Date(conv.updated_time);
        const diffDays = Math.floor(
          (today.getTime() - convDate.getTime()) / (1000 * 60 * 60 * 24)
        );

        if (diffDays === 0) {
          todayConversations.push(conv);
        } else if (diffDays === 1) {
          yesterdayConversations.push(conv);
        } else {
          // 按具体日期分组
          let dateLabel: string;
          if (diffDays < 30) {
            dateLabel = `${convDate.getMonth() + 1}月${convDate.getDate()}日`;
          } else if (diffDays < 365) {
            dateLabel = `${convDate.getMonth() + 1}月${convDate.getDate()}日`;
          } else {
            dateLabel = '更早以前';
          }

          if (!olderConversations[dateLabel]) {
            olderConversations[dateLabel] = [];
          }
          olderConversations[dateLabel].push(conv);
        }
      });

      // 添加分组
      if (todayConversations.length > 0) {
        groups.push({ label: '今天', conversations: todayConversations });
      }
      if (yesterdayConversations.length > 0) {
        groups.push({ label: '昨天', conversations: yesterdayConversations });
      }

      // 添加其他日期分组
      Object.entries(olderConversations).forEach(([dateLabel, convs]) => {
        if (convs.length > 0) {
          groups.push({ label: dateLabel, conversations: convs });
        }
      });

      return groups;
    },

    // 根据ID获取会话
    getConversationById: (conversationId: string) => {
      const state = get();
      return state.conversations.find(c => c.id === conversationId) || null;
    },
  }))
);
