'use client';

import React from 'react';
import { useLocalStorageState } from 'ahooks';
import { AuthProvider } from '@/lib/auth/AuthProvider';
import { GlobalErrorProvider } from '@/providers/GlobalErrorProvider';
import { PageConfigProvider } from '@/contexts/PageConfigContext';
import DashboardContent from '@/components/dashboard/DashboardContent';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [collapsed, setCollapsed] = useLocalStorageState('sidebar-collapsed', {
    defaultValue: false,
  });

  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  return (
    <GlobalErrorProvider>
      <AuthProvider>
        <PageConfigProvider>
          <DashboardContent collapsed={collapsed} onToggle={toggleSidebar}>
            {children}
          </DashboardContent>
        </PageConfigProvider>
      </AuthProvider>
    </GlobalErrorProvider>
  );
}
