import {
  getExtension,
  isSupportedPreviewDocumentType,
} from '@/utils/document-util';
import React, { useState } from 'react';
import DocumentPreviewModal from '@/components/file-manager/modals/document-preview-modal';

interface IProps extends React.PropsWithChildren {
  link?: string;
  preventDefault?: boolean;
  color?: string;
  documentName: string;
  documentId?: string;
  prefix?: string;
  className?: string;
  useModal?: boolean; // 新增：是否使用模态框预览
}

const NewDocumentLink = ({
  children,
  link,
  preventDefault = false,
  color = 'rgb(15, 79, 170)',
  documentId,
  documentName,
  prefix = 'file',
  className,
  useModal = false, // 默认使用新窗口预览
}: IProps) => {
  const [modalVisible, setModalVisible] = useState(false);
  let nextLink = link;
  const extension = getExtension(documentName);

  if (!link) {
    nextLink = `/document/${documentId}?ext=${extension}&prefix=${prefix}`;
  }

  const handleClick = (e: React.MouseEvent) => {
    if (useModal && isSupportedPreviewDocumentType(extension)) {
      e.preventDefault();
      setModalVisible(true);
    } else if (!preventDefault || isSupportedPreviewDocumentType(extension)) {
      // 原有的新窗口预览逻辑
      return;
    } else {
      e.preventDefault();
    }
  };

  const handleModalClose = () => {
    setModalVisible(false);
  };

  return (
    <>
      <a
        target={useModal ? undefined : '_blank'}
        onClick={handleClick}
        href={nextLink}
        rel="noreferrer"
        style={{ color: className ? '' : color, wordBreak: 'break-all' }}
        className={className}
      >
        {children}
      </a>

      {useModal && (
        <DocumentPreviewModal
          visible={modalVisible}
          onClose={handleModalClose}
          documentId={documentId}
          documentName={documentName}
          prefix={prefix}
        />
      )}
    </>
  );
};

export default NewDocumentLink;
